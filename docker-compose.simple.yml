services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    ports:
      - "${PORT:-3011}:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://srmyy_123:<EMAIL>:23387/srmyy_123
      - NEXTAUTH_URL=http://localhost:3011
      - NEXTAUTH_SECRET=your-nextauth-secret-here-change-this-in-production
      - JWT_SECRET=your-jwt-secret-here-change-this-in-production
      - LOG_LEVEL=debug
    volumes:
      - ./public/uploads:/app/public/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s