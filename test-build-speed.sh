#!/bin/bash

# 构建速度测试脚本
set -e

echo "🧪 Docker构建速度测试"
echo "======================"

# 启用BuildKit
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 清理现有镜像以确保公平测试
echo "🧹 清理现有镜像..."
docker rmi lung-function-admin:latest 2>/dev/null || true
docker system prune -f

echo ""
echo "⏱️  开始测试优化版本构建时间..."
echo "================================"

# 记录开始时间
start_time=$(date +%s)

# 构建优化版本
docker-compose -f docker-compose.simple.yml build

# 记录结束时间
end_time=$(date +%s)
build_time=$((end_time - start_time))

echo ""
echo "✅ 构建完成！"
echo "📊 构建时间: ${build_time} 秒"
echo ""

# 测试增量构建
echo "🔄 测试增量构建（模拟代码变更）..."
touch src/app/page.tsx

start_time=$(date +%s)
docker-compose -f docker-compose.simple.yml build
end_time=$(date +%s)
incremental_time=$((end_time - start_time))

echo "📊 增量构建时间: ${incremental_time} 秒"
echo ""

# 显示镜像大小
echo "📦 镜像信息:"
docker images | grep lung-function-admin

echo ""
echo "🎉 测试完成！"
echo "首次构建: ${build_time}秒"
echo "增量构建: ${incremental_time}秒"
