# 完全使用国内镜像的超级优化版本
# 适合中国大陆网络环境

# 使用阿里云的Node.js镜像
FROM registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine AS base

# 配置Alpine使用阿里云镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装必要的系统依赖，包括OpenSSL和Prisma所需的库
RUN apk add --no-cache \
    openssl \
    libc6-compat \
    curl \
    git

# 检查并安装yarn（如果不存在）
RUN which yarn || npm install -g yarn

# 配置npm和yarn使用国内镜像源
RUN npm config set registry https://registry.npmmirror.com && \
    yarn config set registry https://registry.npmmirror.com && \
    yarn config set network-timeout 600000 && \
    yarn config set cache-folder /usr/local/share/.cache/yarn && \
    yarn config set prefer-offline true && \
    yarn config set ignore-engines true

# 设置工作目录
WORKDIR /app

# 仅复制依赖文件以最大化缓存效率
COPY package.json yarn.lock ./

# 预安装阶段 - 仅安装生产依赖
FROM base AS prod-deps
RUN --mount=type=cache,target=/usr/local/share/.cache/yarn,sharing=locked \
    --mount=type=cache,target=/root/.yarn,sharing=locked \
    yarn install --production --frozen-lockfile --prefer-offline --silent

# 开发依赖安装阶段
FROM base AS dev-deps  
RUN --mount=type=cache,target=/usr/local/share/.cache/yarn,sharing=locked \
    --mount=type=cache,target=/root/.yarn,sharing=locked \
    yarn install --frozen-lockfile --prefer-offline --silent

# Prisma生成阶段
FROM dev-deps AS prisma-gen
COPY prisma ./prisma/
RUN npx prisma generate

# 构建阶段
FROM prisma-gen AS builder
# 复制源代码
COPY . .
# 构建应用
RUN yarn build

# 生产运行时阶段 - 使用阿里云镜像
FROM registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine AS runner

# 配置Alpine使用阿里云镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装运行时必要的系统依赖
RUN apk add --no-cache \
    openssl \
    libc6-compat \
    curl

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 复制必要文件
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma
COPY --from=prod-deps /app/node_modules ./node_modules

# 确保Prisma客户端在生产环境中重新生成
RUN npx prisma generate

# 复制public目录
COPY public ./public

# 创建上传目录
RUN mkdir -p /app/public/uploads/avatars && \
    chown -R nextjs:nodejs /app/public/uploads

# 设置用户权限
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 启动应用
CMD ["node", "server.js"]
