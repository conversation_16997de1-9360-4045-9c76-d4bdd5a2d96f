import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { insertDataToDynamicTable, fixTableNumberFields } from '@/lib/dynamicTable'
import { z } from 'zod'

// 金数据Webhook数据验证schema
const jinshujuWebhookSchema = z.object({
  form: z.string(),
  form_name: z.string(),
  entry: z.object({
    serial_number: z.number(),
  }).passthrough(),
})

// POST /api/webhook/[formId] - 接收金数据Webhook推送
export async function POST(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  const formId = params.formId
  const clientIp = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   request.headers.get('remote-addr') || 
                   'unknown'

  try {
    // 获取请求body
    const body = await request.json()
    
    // 验证JSON结构
    const validation = jinshujuWebhookSchema.safeParse(body)
    if (!validation.success) {
      console.error('Webhook数据验证失败:', validation.error.issues)
      return NextResponse.json({
        success: false,
        error: 'Invalid webhook data format',
        details: validation.error.issues,
      }, { status: 400 })
    }

    const webhookData = validation.data

    // 验证表单ID是否匹配
    if (webhookData.form !== formId) {
      console.error(`表单ID不匹配: 期望 ${formId}, 实际 ${webhookData.form}`)
      return NextResponse.json({
        success: false,
        error: 'Form ID mismatch',
      }, { status: 400 })
    }

    // 查找对应的表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isActive: true }
    })

    if (!formConfig) {
      console.error(`表单配置不存在或已禁用: ${formId}`)
      return NextResponse.json({
        success: false,
        error: 'Form configuration not found or disabled',
      }, { status: 404 })
    }

    // 检查是否为重复数据（基于serial_number）
    const tableName = formConfig.tableName
    if (!tableName) {
      console.error(`表单配置缺少表名: ${formId}`)
      return NextResponse.json({
        success: false,
        error: 'Form table name not configured',
      }, { status: 400 })
    }

    const serialNumber = webhookData.entry.serial_number

    const existingRecord = await prisma.$queryRawUnsafe<Array<{ id: bigint }>>(
      `SELECT id FROM \`${tableName}\` WHERE serial_number = ? LIMIT 1`,
      serialNumber
    )

    if (existingRecord.length > 0) {
      console.log(`数据已存在，跳过插入: ${formId}#${serialNumber}`)
      return NextResponse.json({
        success: true,
        message: 'Data already exists, skipped',
        recordId: existingRecord[0].id.toString(),
      })
    }

    // 准备插入数据
    const insertData = {
      ...webhookData.entry,
      source_ip: clientIp,
      creator_name: webhookData.entry.creator_name || null,
    }

    // 检查并修复表结构中的数字字段（如果需要）
    const fixResult = await fixTableNumberFields(
      tableName,
      formConfig.fieldMapping as Record<string, any>
    )

    if (!fixResult.success) {
      console.warn(`修复表结构失败，但继续尝试插入数据: ${fixResult.error}`)
    }

    // 插入数据到动态表
    const insertResult = await insertDataToDynamicTable(
      tableName,
      insertData,
      formConfig.fieldMapping as Record<string, any>
    )

    if (!insertResult.success) {
      throw new Error(`数据插入失败: ${insertResult.error}`)
    }

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: null, // Webhook调用无用户ID
        action: 'WEBHOOK_RECEIVED',
        resource: 'WebhookData',
        resourceId: insertResult.id!.toString(),
        details: {
          formId,
          serialNumber,
          tableName,
          clientIp,
          dataSize: JSON.stringify(webhookData).length,
        },
        ipAddress: clientIp,
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    console.log(`Webhook数据处理成功: ${formId}#${serialNumber}, 记录ID: ${insertResult.id}`)

    return NextResponse.json({
      success: true,
      message: 'Webhook data processed successfully',
      recordId: insertResult.id?.toString(),
      formId,
      serialNumber,
    })

  } catch (error) {
    console.error(`Webhook处理失败 [${formId}]:`, error)

    // 记录错误日志
    try {
      await prisma.systemLog.create({
        data: {
          userId: null,
          action: 'WEBHOOK_ERROR',
          resource: 'WebhookData',
          resourceId: null,
          details: {
            formId,
            error: error instanceof Error ? error.message : 'Unknown error',
            clientIp,
          },
          ipAddress: clientIp,
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      })
    } catch (logError) {
      console.error('记录错误日志失败:', logError)
    }

    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: 'Webhook processing failed',
    }, { status: 500 })
  }
}

// GET /api/webhook/[formId] - 获取Webhook信息（用于测试）
export async function GET(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const formId = params.formId

    // 查找表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId },
      select: {
        formId: true,
        formName: true,
        webhookUrl: true,
        tableName: true,
        fieldCount: true,
        isActive: true,
        createdAt: true,
      }
    })

    if (!formConfig) {
      return NextResponse.json({
        success: false,
        error: 'Form configuration not found',
      }, { status: 404 })
    }

    // 获取最近的数据统计
    const recentDataCount = await prisma.$queryRawUnsafe<Array<{ count: bigint }>>(
      `SELECT COUNT(*) as count FROM \`${formConfig.tableName}\`
       WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)`
    )

    const totalDataCount = await prisma.$queryRawUnsafe<Array<{ count: bigint }>>(
      `SELECT COUNT(*) as count FROM \`${formConfig.tableName}\``
    )

    return NextResponse.json({
      success: true,
      data: {
        ...formConfig,
        stats: {
          totalRecords: Number(totalDataCount[0]?.count || 0),
          recentRecords: Number(recentDataCount[0]?.count || 0),
        },
      },
    })

  } catch (error) {
    console.error('获取Webhook信息失败:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get webhook info',
    }, { status: 500 })
  }
}