#!/bin/bash

# 优化的Docker构建脚本
# 使用BuildKit和缓存优化来加速构建过程

set -e

echo "🚀 开始优化构建..."

# 启用Docker BuildKit
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 设置构建参数
BUILD_ARGS=""
CACHE_FROM=""

# 检查是否有现有镜像可以作为缓存
if docker images | grep -q "lung-function-admin"; then
    echo "📦 发现现有镜像，将用作缓存..."
    CACHE_FROM="--cache-from lung-function-admin:latest"
fi

# 构建镜像
echo "🔨 开始构建镜像..."
docker-compose -f docker-compose.simple.yml build \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    $CACHE_FROM \
    --progress=plain

echo "✅ 构建完成！"

# 可选：清理悬空镜像
read -p "是否清理悬空镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理悬空镜像..."
    docker image prune -f
fi

echo "🎉 所有操作完成！"
